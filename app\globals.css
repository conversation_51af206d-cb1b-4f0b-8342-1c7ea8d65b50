@tailwind base;
@tailwind components;
@tailwind utilities;
/* @tailwind components;
@tailwind utilities; */

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Focus styles */
input:focus,
textarea:focus,
select:focus,
button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Custom button hover effects */
.btn-hover {
  transition: all 0.3s ease;
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Card animations */
.card-enter {
  animation: fadeIn 0.5s ease-out;
}

.slide-enter {
  animation: slideIn 0.3s ease-out;
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Mobile menu overlay */
.mobile-overlay {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Custom checkbox styles */
input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

/* Responsive text scaling */
@media (max-width: 768px) {
  .text-responsive-xl {
    font-size: 1.5rem;
  }

  .text-responsive-2xl {
    font-size: 1.75rem;
  }

  .text-responsive-3xl {
    font-size: 2rem;
  }
}

@media (min-width: 769px) {
  .text-responsive-xl {
    font-size: 1.75rem;
  }

  .text-responsive-2xl {
    font-size: 2rem;
  }

  .text-responsive-3xl {
    font-size: 2.5rem;
  }
}

/* Pattern backgrounds */
.pattern-dots {
  background-image: radial-gradient(circle, #3b82f6 2px, transparent 2px);
  background-size: 30px 30px;
}

.pattern-grid {
  background-image: linear-gradient(#3b82f6 1px, transparent 1px), linear-gradient(90deg, #3b82f6 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Slide transitions */
.slide-transition {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

.slide-enter-active {
  transform: translateX(0);
  opacity: 1;
}

.slide-enter {
  transform: translateX(100%);
  opacity: 0;
}

.slide-leave-active {
  transform: translateX(-100%);
  opacity: 0;
}

/* Custom range inputs */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #e5e7eb;
  height: 6px;
  border-radius: 3px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #3b82f6;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.button-wave-bg {
  background: linear-gradient(270deg, #2563eb, #6366f1, #a21caf, #6366f1, #2563eb);
  background-size: 400% 100%;
  animation: waveMove 4s linear infinite;
}
@keyframes waveMove {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}
