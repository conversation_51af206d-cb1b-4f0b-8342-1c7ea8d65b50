
"use client"

interface DesignSettings {
  backgroundElement: string
  backgroundEnabled: boolean
  colorPalette: number
  useCustomColors: boolean
  customColors: string[]
  fontSize: {
    title: string
    subtitle: string
    content: string
  }
  fontFamily: string
  slideLayout: string
}

interface UserProfile {
  name: string
  handle: string
  avatar: string
}

interface SettingsPanelProps {
  designSettings: DesignSettings
  onSettingsChange: (settings: DesignSettings) => void
  userProfile: UserProfile
  onUserProfileChange: (updates: Partial<UserProfile>) => void
}

// Utility function to resize images
function resizeImage(file: File, maxSize = 96): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new window.Image();
    const reader = new FileReader();
    reader.onload = (e) => {
      img.onload = () => {
        let width = img.width;
        let height = img.height;
        if (width > height) {
          if (width > maxSize) {
            height *= maxSize / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width *= maxSize / height;
            height = maxSize;
          }
        }
        const canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext("2d");
        if (!ctx) return reject(new Error("Could not get canvas context"));
        ctx.drawImage(img, 0, 0, width, height);
        // Lower quality for smaller size
        const dataUrl = canvas.toDataURL("image/jpeg", 0.5);
        // Check size (100KB limit)
        const sizeKB = Math.round((dataUrl.length * 3) / 4 / 1024);
        if (sizeKB > 100) {
          return reject(new Error("Image is too large after resizing. Please choose a smaller image."));
        }
        resolve(dataUrl);
      };
      img.onerror = reject;
      if (e.target && typeof e.target.result === "string") {
        img.src = e.target.result;
      } else {
        reject(new Error("Invalid image data"));
      }
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

export default function SettingsPanel({ designSettings, onSettingsChange, userProfile, onUserProfileChange }: SettingsPanelProps) {
  const updateSettings = (updates: Partial<DesignSettings>) => {
    onSettingsChange({ ...designSettings, ...updates })
  }

  const updateFontSize = (type: keyof DesignSettings["fontSize"], size: string) => {
    updateSettings({
      fontSize: {
        ...designSettings.fontSize,
        [type]: size,
      },
    })
  }

  const fontSizeOptions = [


    { value: "text-3xl", label: "Small" },
    { value: "text-4xl", label: "Large" },
    { value: "text-5xl", label: "Extra Large" },
    { value: "text-6xl", label: "2X Large" },
    { value: "text-7xl", label: "3X Large" },
    // { value: "text-8xl", label: "4X Large" },
    // { value: "text-9xl", label: "5X Large" },
    // { value: "text-10xl", label: "6X Large" },
  ]


  const fontSizeOptions1 = [


    { value: "text-lg", label: "Small" },
    { value: "text-xl", label: "Large" },
    { value: "text-2xl", label: "Extra Large" },
    { value: "text-3xl", label: "2X Large" },
    { value: "text-4xl", label: "3X Large" },
    // { value: "text-8xl", label: "4X Large" },
    // { value: "text-9xl", label: "5X Large" },
    // { value: "text-10xl", label: "6X Large" },
  ]

  const fontSizeOptions2 = [


    { value: "text-sm", label: "Small" },
    { value: "text-md", label: "Medium" },
    { value: "text-lg", label: "Large" },


    // { value: "text-8xl", label: "4X Large" },
    // { value: "text-9xl", label: "5X Large" },
    // { value: "text-10xl", label: "6X Large" },
  ]

  const fontFamilyOptions = [
    { value: 'font-sans', label: 'Sans (Default)' },
    { value: 'font-serif', label: 'Serif' },
    { value: 'font-mono', label: 'Monospace' },
    { value: 'font-poppins', label: 'Poppins' },
    // { value: 'font-nunito', label: 'Nunito' },
    // { value: 'font-robotoslab', label: 'Roboto Slab' },
    // { value: 'font-lato', label: 'Lato' },
    // { value: 'font-montserrat', label: 'Montserrat' },
    { value: 'font-merriweather', label: 'Merriweather' },
    { value: 'font-pacifico', label: 'Pacifico' },
    { value: 'font-oswald', label: 'Oswald' },
    { value: 'font-dancingscript', label: 'Dancing Script' },
  ];

  const layoutOptions = [
    { value: "centered", label: "Centered" },
    { value: "standard", label: "Standard" },
    { value: "right-aligned", label: "Right Aligned" }
    // { value: "left-aligned", label: "Left Aligned" },
    // { value: "minimal", label: "Minimal" },
  ]

  return (
    <div className="h-full p-6 ">
      <h3 className="text-xl font-semibold mb-6">Settings</h3>
        <div className="space-y-6">
          {/* User Profile Section */}
          <div className="rounded-lg">
            <h4 className="font-medium text-gray-900 mb-4">Profile Information</h4>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Profile Photo</label>
                <div className="flex items-center space-x-4">
                  <label className="relative cursor-pointer">
                    <img
                      src={userProfile.avatar}
                      alt="Profile"
                      className="w-12 h-12 rounded-full object-cover border"
                    />
                    <input
                      type="file"
                      accept="image/*"
                      className="absolute inset-0 opacity-0 cursor-pointer"
                      onChange={async e => {
                        const file = e.target.files?.[0];
                        if (file) {
                          try {
                            const resized = await resizeImage(file, 96);
                            onUserProfileChange({ avatar: resized });
                          } catch (err: any) {
                            alert(err.message || "Failed to process image.");
                          }
                        }
                      }}
                    />
                  </label>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  value={userProfile.name}
                  onChange={e => onUserProfileChange({ name: e.target.value })}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Profile ID</label>
                <input
                  type="text"
                  value={userProfile.handle}
                  onChange={e => onUserProfileChange({ handle: e.target.value })}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="@username"
                />
              </div>
            </div>
          </div>

          {/* Slide Layout */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Slide Layout</label>
            <select
              value={designSettings.slideLayout}
              onChange={(e) => updateSettings({ slideLayout: e.target.value })}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {layoutOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Font Family */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Font Family</label>
            <select
              value={designSettings.fontFamily}
              onChange={(e) => updateSettings({ fontFamily: e.target.value })}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {fontFamilyOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Font Sizes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Font Sizes</label>
            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Title</label>
                <select
                  value={designSettings.fontSize.title}
                  onChange={(e) => updateFontSize("title", e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {fontSizeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Subtitle</label>
                <select
                  value={designSettings.fontSize.subtitle}
                  onChange={(e) => updateFontSize("subtitle", e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {fontSizeOptions1.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Content</label>
                <select
                  value={designSettings.fontSize.content}
                  onChange={(e) => updateFontSize("content", e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {fontSizeOptions2.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
      </div>
    </div>
  )
}
